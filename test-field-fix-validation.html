<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字段映射修复验证</title>
    <style>
        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success { background: #d4edda; border-color: #28a745; color: #155724; }
        .error { background: #f8d7da; border-color: #dc3545; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffc107; color: #856404; }
        .info { background: #d1ecf1; border-color: #17a2b8; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 字段映射修复验证</h1>
        <p>验证 arrival_time → pickupTime 映射和地址翻译功能</p>

        <div class="test-section">
            <h3>📊 测试数据</h3>
            <div class="code-block" id="testData">
{
  "customer_name": "梁嘉依",
  "arrival_time": "19:00",
  "time": null,
  "pickup": "斗湖机场MAIN",
  "destination": "永达大酒店",
  "date": "2025-08-15"
}
            </div>
            <button onclick="testFieldMapping()">测试字段映射</button>
            <button onclick="testTimeFieldPriority()">测试时间字段优先级</button>
            <button onclick="clearResults()">清空结果</button>
        </div>

        <div class="test-section">
            <h3>🔍 映射结果验证</h3>
            <div id="mappingResults"></div>
        </div>

        <div class="test-section">
            <h3>🚀 地址翻译测试</h3>
            <button onclick="testAddressTranslation()">测试地址翻译</button>
            <div id="addressResults"></div>
        </div>
    </div>

    <script>
        // 测试数据
        const testData = {
            "customer_name": "梁嘉依",
            "customer_contact": "18057562938",
            "arrival_time": "19:00",
            "departure_time": null,
            "pickup_time": null,
            "time": null,
            "pickup": "斗湖机场MAIN",
            "destination": "永达大酒店",
            "date": "2025-08-15",
            "passenger_number": 4
        };

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('mappingResults').innerHTML = '';
            document.getElementById('addressResults').innerHTML = '';
        }

        function testFieldMapping() {
            addResult('mappingResults', '=== 开始字段映射测试 ===', 'info');
            
            // 模拟 FormManager 的字段映射表
            const fieldMapping = {
                'customer_name': 'customerName',
                'time': 'pickupTime',
                'arrival_time': 'pickupTime',
                'departure_time': 'pickupTime',
                'pickup_time': 'pickupTime',
                'pickup': 'pickup',
                'destination': 'dropoff',
                'date': 'pickupDate'
            };

            addResult('mappingResults', '🔍 字段映射表验证:', 'info');
            for (const [key, value] of Object.entries(fieldMapping)) {
                if (testData.hasOwnProperty(key)) {
                    const dataValue = testData[key];
                    addResult('mappingResults', 
                        `${key} → ${value} = ${dataValue}`, 
                        dataValue ? 'success' : 'warning'
                    );
                }
            }

            // 测试时间字段优先级
            addResult('mappingResults', '', 'info');
            addResult('mappingResults', '⏰ 时间字段优先级测试:', 'info');
            
            const timeFields = ['pickup_time', 'arrival_time', 'departure_time', 'time'];
            const timePriorities = { 'pickup_time': 1, 'arrival_time': 2, 'departure_time': 3, 'time': 4 };
            
            let selectedTime = null;
            let selectedPriority = 999;
            
            timeFields.forEach(field => {
                if (testData[field]) {
                    const priority = timePriorities[field] || 5;
                    addResult('mappingResults', 
                        `${field}: ${testData[field]} (优先级: ${priority})`, 
                        'info'
                    );
                    
                    if (priority < selectedPriority) {
                        selectedTime = testData[field];
                        selectedPriority = priority;
                    }
                }
            });

            if (selectedTime) {
                addResult('mappingResults', 
                    `✅ 应该选择时间: ${selectedTime} (优先级: ${selectedPriority})`, 
                    'success'
                );
            } else {
                addResult('mappingResults', 
                    '❌ 没有找到有效的时间值', 
                    'error'
                );
            }

            // 检查实际DOM中的值
            const pickupTimeElement = document.getElementById('pickupTime');
            if (pickupTimeElement) {
                addResult('mappingResults', 
                    `🎯 实际表单中的时间值: ${pickupTimeElement.value || '(空)'}`, 
                    pickupTimeElement.value ? 'success' : 'warning'
                );
            } else {
                addResult('mappingResults', 
                    '⚠️ 未找到 pickupTime 元素 - 这可能是问题所在', 
                    'warning'
                );
            }
        }

        function testTimeFieldPriority() {
            addResult('mappingResults', '=== 时间字段优先级详细测试 ===', 'info');
            
            // 测试各种时间字段组合
            const testCases = [
                { arrival_time: "19:00", time: null, expected: "19:00" },
                { arrival_time: "19:00", departure_time: "18:00", expected: "18:00" },
                { pickup_time: "17:00", arrival_time: "19:00", expected: "17:00" },
                { time: "20:00", arrival_time: null, expected: "20:00" }
            ];

            testCases.forEach((testCase, index) => {
                addResult('mappingResults', `测试用例 ${index + 1}:`, 'info');
                
                let selectedTime = null;
                let selectedField = null;
                let minPriority = 999;
                
                for (const [field, value] of Object.entries(testCase)) {
                    if (field === 'expected') continue;
                    
                    if (value !== null && value !== undefined) {
                        const priority = {
                            'pickup_time': 1,
                            'arrival_time': 2,
                            'departure_time': 3,
                            'time': 4
                        }[field] || 5;
                        
                        if (priority < minPriority) {
                            selectedTime = value;
                            selectedField = field;
                            minPriority = priority;
                        }
                        
                        addResult('mappingResults', `  ${field}: ${value} (优先级: ${priority})`, 'info');
                    }
                }
                
                const expected = testCase.expected;
                const success = selectedTime === expected;
                
                addResult('mappingResults', 
                    `  结果: ${selectedTime} (来自 ${selectedField}) | 预期: ${expected}`,
                    success ? 'success' : 'error'
                );
            });
        }

        function testAddressTranslation() {
            addResult('addressResults', '=== 地址翻译测试 ===', 'info');
            
            const addresses = [
                { address: '斗湖机场MAIN', expected: 'Tawau Airport MAIN' },
                { address: '永达大酒店', expected: 'Wing Tat Grand Hotel' }
            ];

            addresses.forEach(({ address, expected }) => {
                addResult('addressResults', `🔍 测试地址: ${address}`, 'info');
                addResult('addressResults', `📝 预期结果: ${expected}`, 'info');
                
                // 检查是否有相关的DOM元素
                const pickupField = document.getElementById('pickup');
                const dropoffField = document.getElementById('dropoff');
                
                if (address === '斗湖机场MAIN' && pickupField) {
                    const actualValue = pickupField.value;
                    addResult('addressResults', 
                        `🎯 实际pickup值: ${actualValue}`,
                        actualValue === expected ? 'success' : 'warning'
                    );
                }
                
                if (address === '永达大酒店' && dropoffField) {
                    const actualValue = dropoffField.value;
                    addResult('addressResults', 
                        `🎯 实际dropoff值: ${actualValue}`,
                        actualValue === expected ? 'success' : 'warning'
                    );
                }
            });

            // 检查地址处理器是否存在
            if (window.OTA && window.OTA.addressProcessor) {
                addResult('addressResults', '✅ 地址处理器已加载', 'success');
            } else {
                addResult('addressResults', '❌ 地址处理器未找到', 'error');
            }

            // 检查简化地址处理器
            if (window.getSimpleAddressProcessor) {
                try {
                    const processor = window.getSimpleAddressProcessor();
                    if (processor) {
                        addResult('addressResults', '✅ 简化地址处理器已加载', 'success');
                    } else {
                        addResult('addressResults', '❌ 简化地址处理器实例为空', 'error');
                    }
                } catch (error) {
                    addResult('addressResults', `❌ 简化地址处理器错误: ${error.message}`, 'error');
                }
            } else {
                addResult('addressResults', '❌ getSimpleAddressProcessor 函数未找到', 'error');
            }
        }

        // 页面加载完成后的初始检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                addResult('mappingResults', '📋 页面加载完成，开始环境检查...', 'info');
                
                // 检查关键对象
                const checks = [
                    { name: 'window.OTA', obj: window.OTA },
                    { name: 'window.OTA.managers', obj: window.OTA?.managers },
                    { name: 'FormManager', obj: window.OTA?.managers?.FormManager },
                    { name: 'Container', obj: window.OTA?.container }
                ];

                checks.forEach(({ name, obj }) => {
                    addResult('mappingResults', 
                        `${name}: ${obj ? '存在' : '不存在'}`,
                        obj ? 'success' : 'warning'
                    );
                });

                // 自动运行基本测试
                setTimeout(testFieldMapping, 500);
            }, 1000);
        });
    </script>
</body>
</html>