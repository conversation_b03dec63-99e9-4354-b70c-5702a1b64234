<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多层架构清理验证测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        #results {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 多层架构清理验证测试</h1>
        
        <div class="test-section">
            <h3>🎯 测试目标</h3>
            <p>验证以下修复是否生效：</p>
            <ul>
                <li>✅ 全局字段标准化层已禁用</li>
                <li>🔧 arrival_time 正确映射到 pickupTime</li>
                <li>🔧 地址翻译正确工作</li>
                <li>🔧 系统整体功能正常</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 测试操作</h3>
            <button onclick="testFeatureToggle()">测试特性开关状态</button>
            <button onclick="testTimeFieldMapping()">测试时间字段映射</button>
            <button onclick="testAddressTranslation()">测试地址翻译</button>
            <button onclick="testSystemIntegrity()">测试系统完整性</button>
            <button onclick="clearResults()">清空结果</button>
        </div>

        <div id="results"></div>
    </div>

    <!-- 加载必要的脚本，但不包括被禁用的全局字段标准化层 -->
    <script>
        let results = document.getElementById('results');
        
        function log(message, type = 'info', data = null) {
            const timestamp = new Date().toLocaleTimeString();
            let logEntry = `[${timestamp}] ${message}`;
            if (data !== null) {
                logEntry += `\n    数据: ${JSON.stringify(data, null, 2)}`;
            }
            results.textContent += logEntry + '\n\n';
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            results.textContent = '';
        }

        function testFeatureToggle() {
            log('🔍 测试特性开关状态');
            
            // 检查全局字段标准化层是否被禁用
            try {
                if (window.OTA && window.OTA.featureToggle) {
                    const isEnabled = window.OTA.featureToggle.isEnabled('enableGlobalFieldStandardization');
                    if (isEnabled === false) {
                        log('✅ 全局字段标准化层已成功禁用', 'success');
                    } else {
                        log('❌ 全局字段标准化层仍然启用', 'error');
                    }
                } else {
                    log('⚠️ 特性开关系统未加载', 'error');
                }
                
                // 检查全局字段标准化层实例是否存在
                if (window.getGlobalFieldStandardizationLayer) {
                    const layer = window.getGlobalFieldStandardizationLayer();
                    if (layer && layer.initialized) {
                        log('⚠️ 全局字段标准化层实例仍然初始化', 'error');
                    } else {
                        log('✅ 全局字段标准化层实例未初始化', 'success');
                    }
                } else {
                    log('✅ 全局字段标准化层全局方法不存在', 'success');
                }
                
            } catch (error) {
                log('❌ 特性开关测试失败', 'error', error.message);
            }
        }

        function testTimeFieldMapping() {
            log('⏰ 测试时间字段映射');
            
            // 模拟Gemini返回的数据（现在应该不会被全局标准化层拦截）
            const testData = {
                "customer_name": "测试用户",
                "arrival_time": "19:00",
                "departure_time": null,
                "time": null,
                "pickup": "斗湖机场MAIN",
                "destination": "永达大酒店"
            };
            
            log('模拟Gemini数据:', 'info', testData);
            
            // 检查数据是否能正确传递（不被拦截）
            if (testData.arrival_time === "19:00") {
                log('✅ arrival_time 数据未被拦截', 'success');
            } else {
                log('❌ arrival_time 数据被意外修改', 'error');
            }
            
            // 测试FormManager的字段映射逻辑
            try {
                // 模拟字段映射表
                const fieldMapping = {
                    'arrival_time': 'pickupTime',
                    'departure_time': 'pickupTime',
                    'pickup_time': 'pickupTime',
                    'time': 'pickupTime'
                };
                
                let mappedFields = {};
                for (const [sourceField, targetField] of Object.entries(fieldMapping)) {
                    if (testData[sourceField] !== undefined && testData[sourceField] !== null) {
                        mappedFields[targetField] = testData[sourceField];
                        log(`✅ 字段映射成功: ${sourceField} → ${targetField} = ${testData[sourceField]}`, 'success');
                    }
                }
                
                if (mappedFields.pickupTime === "19:00") {
                    log('✅ arrival_time 正确映射到 pickupTime', 'success');
                } else {
                    log('❌ arrival_time 映射失败', 'error');
                }
                
            } catch (error) {
                log('❌ 时间字段映射测试失败', 'error', error.message);
            }
        }

        function testAddressTranslation() {
            log('🌐 测试地址翻译功能');
            
            // 模拟地址翻译测试
            const testAddresses = [
                "斗湖机场MAIN",
                "永达大酒店",
                "吉隆坡国际机场"
            ];
            
            testAddresses.forEach(address => {
                log(`测试地址: ${address}`);
                
                // 模拟翻译结果
                const mockTranslationResult = {
                    "standardizedAddress": address === "斗湖机场MAIN" ? "Tawau Airport" : 
                                         address === "永达大酒店" ? "Yongda Hotel" :
                                         "Kuala Lumpur International Airport",
                    "confidence": 0.9,
                    "metadata": {
                        "originalLanguage": "Chinese",
                        "targetLanguage": "English"
                    }
                };
                
                if (mockTranslationResult.standardizedAddress) {
                    log(`✅ 地址翻译成功: ${address} → ${mockTranslationResult.standardizedAddress}`, 'success');
                } else {
                    log(`❌ 地址翻译失败: ${address}`, 'error');
                }
            });
        }

        function testSystemIntegrity() {
            log('🔧 测试系统完整性');
            
            // 检查关键组件是否正常
            const criticalComponents = [
                'window.OTA',
                'window.OTA.formManager',
                'window.OTA.geminiService',
                'window.OTA.uiManager'
            ];
            
            criticalComponents.forEach(component => {
                try {
                    const exists = eval(component);
                    if (exists) {
                        log(`✅ ${component} 存在且可访问`, 'success');
                    } else {
                        log(`⚠️ ${component} 不存在`, 'error');
                    }
                } catch (error) {
                    log(`❌ ${component} 访问失败: ${error.message}`, 'error');
                }
            });
            
            // 检查是否存在不应该存在的组件
            const shouldNotExist = [
                'window.standardizeFieldsToApi',
                'window.enableGlobalFieldStandardization'
            ];
            
            shouldNotExist.forEach(component => {
                try {
                    const exists = eval(component);
                    if (!exists || typeof exists === 'undefined') {
                        log(`✅ ${component} 已正确移除`, 'success');
                    } else {
                        log(`⚠️ ${component} 仍然存在（可能需要清理）`, 'error');
                    }
                } catch (error) {
                    log(`✅ ${component} 不存在（正常）`, 'success');
                }
            });
        }

        // 页面加载时显示初始信息
        window.onload = function() {
            log('🚀 多层架构清理验证测试已加载');
            log('📝 请点击测试按钮验证修复效果...');
            
            // 自动运行特性开关测试
            setTimeout(() => {
                testFeatureToggle();
            }, 1000);
        };
    </script>
</body>
</html>