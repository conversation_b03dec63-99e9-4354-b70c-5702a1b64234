<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新文件引用测试</title>
    <style>
        body {
            font-family: 'Segoe UI', <PERSON>l, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 新文件引用验证测试</h1>
        <p>验证新添加的文件是否能正确通过script-manifest加载</p>

        <button onclick="testScriptManifest()">测试Script Manifest</button>
        <button onclick="testKeyFiles()">测试关键文件加载</button>
        <button onclick="clearResults()">清空结果</button>

        <div id="results" style="margin-top: 20px;"></div>
    </div>

    <!-- 只加载必要的核心文件进行测试 -->
    <script src="js/core/script-manifest.js"></script>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            resultsDiv.appendChild(statusDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function testScriptManifest() {
            addResult('=== Script Manifest 测试 ===', 'info');
            
            // 检查manifest是否正确加载
            if (window.OTA && window.OTA.scriptManifest) {
                addResult('✅ Script Manifest 已加载', 'success');
                
                const manifest = window.OTA.scriptManifest;
                addResult(`📊 版本: ${manifest.version}`, 'info');
                addResult(`📊 阶段数: ${manifest.phases.length}`, 'info');
                
                // 检查关键文件是否在manifest中
                const keyFiles = [
                    'js/flow/simple-address-processor.js',
                    'js/core/script-loader.js', 
                    'js/core/component-lifecycle-manager.js',
                    'js/core/vehicle-config-integration.js'
                ];
                
                const manifestStr = JSON.stringify(manifest);
                let allFound = true;
                
                keyFiles.forEach(file => {
                    if (manifestStr.includes(file)) {
                        addResult(`✅ 发现文件: ${file}`, 'success');
                    } else {
                        addResult(`❌ 缺失文件: ${file}`, 'error');
                        allFound = false;
                    }
                });
                
                if (allFound) {
                    addResult('🎉 所有关键文件都正确引用！', 'success');
                } else {
                    addResult('⚠️ 部分文件缺失引用', 'error');
                }
                
            } else {
                addResult('❌ Script Manifest 未加载', 'error');
            }
        }

        function testKeyFiles() {
            addResult('=== 关键文件检测 ===', 'info');
            
            // 模拟检查这些文件是否能被找到（通过fetch测试）
            const keyFiles = [
                'js/flow/simple-address-processor.js',
                'js/core/script-loader.js', 
                'js/core/component-lifecycle-manager.js',
                'js/core/vehicle-config-integration.js'
            ];

            let testCount = 0;
            const totalTests = keyFiles.length;
            
            keyFiles.forEach(file => {
                fetch(file, { method: 'HEAD' })
                    .then(response => {
                        testCount++;
                        if (response.ok) {
                            addResult(`✅ 文件存在: ${file}`, 'success');
                        } else {
                            addResult(`❌ 文件不存在: ${file} (${response.status})`, 'error');
                        }
                        
                        if (testCount === totalTests) {
                            addResult('📋 文件存在性检查完成', 'info');
                        }
                    })
                    .catch(error => {
                        testCount++;
                        addResult(`❌ 无法访问: ${file} (${error.message})`, 'error');
                        
                        if (testCount === totalTests) {
                            addResult('📋 文件存在性检查完成', 'info');
                        }
                    });
            });
        }

        // 页面加载完成后自动运行基本测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                addResult('🚀 页面加载完成，开始自动测试...', 'info');
                testScriptManifest();
            }, 500);
        });
    </script>
</body>
</html>