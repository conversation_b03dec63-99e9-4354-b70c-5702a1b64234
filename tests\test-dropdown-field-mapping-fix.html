<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下拉菜单修复和字段映射测试</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/multi-order-cards.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        
        .test-title {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background-color: #4CAF50; }
        .status-warning { background-color: #FF9800; }
        .status-error { background-color: #f44336; }
        
        .test-controls {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        
        .test-controls button {
            margin: 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        
        .test-controls button:hover {
            background-color: #0056b3;
        }
        
        .test-data {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        
        .log-area {
            height: 200px;
            overflow-y: auto;
            background: #1e1e1e;
            color: #fff;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .dropdown-test {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>多订单系统 - 下拉菜单和字段映射测试</h1>
        
        <!-- 下拉菜单测试部分 -->
        <div class="test-section">
            <h2 class="test-title">
                <span class="status-indicator status-warning"></span>
                下拉菜单收缩测试
            </h2>
            
            <div class="test-controls">
                <button onclick="testDropdownCollapse()">测试下拉收缩</button>
                <button onclick="generateTestOrders()">生成测试订单</button>
                <button onclick="clearLogs()">清空日志</button>
            </div>
            
            <div class="dropdown-test">
                <p><strong>测试说明：</strong></p>
                <ul>
                    <li>点击"行驶区域"或"车型"字段进入编辑模式</li>
                    <li>下拉菜单应该在以下情况下自动收缩：
                        <ul>
                            <li>点击菜单外的其他区域</li>
                            <li>按下 ESC 键</li>
                            <li>字段失去焦点</li>
                            <li>选择了选项后</li>
                        </ul>
                    </li>
                </ul>
            </div>
            
            <div id="multiOrderContainer">
                <!-- 多订单显示区域 -->
            </div>
        </div>
        
        <!-- 字段映射测试部分 -->
        <div class="test-section">
            <h2 class="test-title">
                <span class="status-indicator status-warning"></span>
                字段映射测试
            </h2>
            
            <div class="test-controls">
                <button onclick="testFieldMapping()">测试字段映射</button>
                <button onclick="testGeminiFieldMapping()">测试Gemini字段映射</button>
                <button onclick="validateAllMappings()">验证所有映射</button>
            </div>
            
            <div class="test-data" id="fieldMappingResult">
                等待测试结果...
            </div>
        </div>
        
        <!-- 日志区域 -->
        <div class="test-section">
            <h2 class="test-title">
                <span class="status-indicator status-success"></span>
                测试日志
            </h2>
            <div class="log-area" id="testLogs"></div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <!-- 🧹 架构简化：field-mapping-config.js 已移除，配置已内置到相关组件 -->
    <script src="js/multi-order-manager-v2.js"></script>

    <script>
        // 测试数据
        const testOrdersData = [
            {
                customer_name: "张三",
                customer_contact: "13800138000",
                pickup_location: "浦东机场",
                destination: "静安区南京路",
                pickup_date: "2024-01-15",
                pickup_time: "14:30",
                ota_price: 150,
                ota_channel: "携程",
                vehicle_type_id: "1",
                driving_region_id: "2",
                passenger_count: 2,
                luggage_count: 1
            },
            {
                customer_name: "李四",
                customer_contact: "13900139000",
                pickup_location: "虹桥机场",
                destination: "徐汇区淮海路",
                pickup_date: "2024-01-16",
                pickup_time: "09:00",
                ota_price: 200,
                ota_channel: "飞猪",
                vehicle_type_id: "2",
                driving_region_id: "1",
                passenger_count: 3,
                luggage_count: 2
            }
        ];

        // 模拟系统数据
        const mockSystemData = {
            carTypes: [
                { id: "1", name: "经济型轿车" },
                { id: "2", name: "商务车" },
                { id: "3", name: "豪华轿车" }
            ],
            drivingRegions: [
                { id: "1", name: "市区" },
                { id: "2", name: "浦东新区" },
                { id: "3", name: "郊区" }
            ]
        };

        let multiOrderManager;
        let testLogElement;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            testLogElement = document.getElementById('testLogs');
            log('页面加载完成，开始初始化...');
            
            // 初始化应用状态
            if (typeof getAppState === 'function') {
                const appState = getAppState();
                appState.set('systemData', mockSystemData);
                log('✅ 应用状态已初始化');
            }
            
            // 初始化多订单管理器
            if (typeof MultiOrderManagerV2 === 'function') {
                multiOrderManager = new MultiOrderManagerV2('multiOrderContainer');
                log('✅ 多订单管理器已初始化');
            } else {
                log('❌ MultiOrderManagerV2 未找到');
            }
        });

        // 日志函数
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            console.log(logMessage);
            
            if (testLogElement) {
                testLogElement.innerHTML += logMessage + '\n';
                testLogElement.scrollTop = testLogElement.scrollHeight;
            }
        }

        // 清空日志
        function clearLogs() {
            if (testLogElement) {
                testLogElement.innerHTML = '';
            }
        }

        // 生成测试订单
        function generateTestOrders() {
            log('开始生成测试订单...');
            
            if (!multiOrderManager) {
                log('❌ 多订单管理器未初始化');
                return;
            }
            
            try {
                multiOrderManager.state.parsedOrders = [...testOrdersData];
                multiOrderManager.renderMultiOrderCards();
                log(`✅ 已生成 ${testOrdersData.length} 个测试订单`);
            } catch (error) {
                log(`❌ 生成测试订单失败: ${error.message}`);
            }
        }

        // 测试下拉菜单收缩
        function testDropdownCollapse() {
            log('开始测试下拉菜单收缩功能...');
            
            // 检查是否有订单
            if (!multiOrderManager || !multiOrderManager.state.parsedOrders.length) {
                log('⚠️ 请先生成测试订单');
                return;
            }
            
            log('✅ 下拉菜单收缩测试准备完成');
            log('📝 请手动测试：');
            log('  1. 点击任意订单的"行驶区域"字段');
            log('  2. 观察下拉菜单是否正确显示');
            log('  3. 点击页面其他区域，菜单应该收缩');
            log('  4. 重复测试"车型"字段');
        }

        // 测试字段映射
        function testFieldMapping() {
            log('开始测试字段映射...');
            
            if (!multiOrderManager) {
                log('❌ 多订单管理器未初始化');
                return;
            }
            
            // 测试各种字段映射
            const testFields = [
                { field: 'vehicleType', value: '1' },
                { field: 'drivingRegion', value: '2' },
                { field: 'customerName', value: '测试用户' },
                { field: 'pickup', value: '测试上车点' }
            ];
            
            let results = [];
            
            testFields.forEach(test => {
                try {
                    const mappedValue = multiOrderManager.getFieldValue(
                        testOrdersData[0], 
                        test.field
                    );
                    results.push(`${test.field}: ${mappedValue}`);
                    log(`✅ ${test.field} 映射成功: ${mappedValue}`);
                } catch (error) {
                    results.push(`${test.field}: 映射失败 - ${error.message}`);
                    log(`❌ ${test.field} 映射失败: ${error.message}`);
                }
            });
            
            document.getElementById('fieldMappingResult').textContent = results.join('\n');
        }

        // 测试Gemini字段映射
        function testGeminiFieldMapping() {
            log('开始测试Gemini字段映射...');
            
            // 模拟Gemini返回的数据
            const geminiMockData = {
                客户姓名: "王五",
                客户联系方式: "13700137000",
                上车地点: "浦东机场T2",
                目的地: "静安区",
                用车日期: "2024-01-20",
                用车时间: "16:00",
                车型ID: "1",
                行驶区域ID: "2",
                乘客人数: "4",
                行李数量: "3",
                特殊要求: "需要儿童座椅"
            };
            
            if (!multiOrderManager) {
                log('❌ 多订单管理器未初始化');
                return;
            }
            
            let results = [];
            
            // 测试各种Gemini字段的映射
            Object.keys(geminiMockData).forEach(geminiField => {
                try {
                    const value = geminiMockData[geminiField];
                    log(`测试Gemini字段: ${geminiField} = ${value}`);
                    results.push(`${geminiField}: ${value}`);
                } catch (error) {
                    results.push(`${geminiField}: 处理失败 - ${error.message}`);
                    log(`❌ ${geminiField} 处理失败: ${error.message}`);
                }
            });
            
            document.getElementById('fieldMappingResult').textContent = 
                'Gemini字段映射测试:\n' + results.join('\n');
            
            log('✅ Gemini字段映射测试完成');
        }

        // 验证所有映射
        function validateAllMappings() {
            log('开始验证所有字段映射...');
            
            if (!window.fieldMappingConfig) {
                log('❌ 字段映射配置未加载');
                return;
            }
            
            const config = window.fieldMappingConfig;
            let results = [];
            
            // 验证AI_TO_FRONTEND映射
            if (config.AI_TO_FRONTEND) {
                Object.keys(config.AI_TO_FRONTEND).forEach(aiField => {
                    const frontendField = config.AI_TO_FRONTEND[aiField];
                    results.push(`AI字段 "${aiField}" → 前端字段 "${frontendField}"`);
                });
                log(`✅ AI_TO_FRONTEND映射验证完成，共${Object.keys(config.AI_TO_FRONTEND).length}个映射`);
            }
            
            // 验证ALTERNATIVE_FIELDS映射
            if (config.ALTERNATIVE_FIELDS) {
                Object.keys(config.ALTERNATIVE_FIELDS).forEach(field => {
                    const alternatives = config.ALTERNATIVE_FIELDS[field];
                    results.push(`字段 "${field}" 的备选字段: [${alternatives.join(', ')}]`);
                });
                log(`✅ ALTERNATIVE_FIELDS映射验证完成，共${Object.keys(config.ALTERNATIVE_FIELDS).length}个字段`);
            }
            
            document.getElementById('fieldMappingResult').textContent = 
                '所有字段映射验证:\n' + results.join('\n');
            
            log('✅ 所有字段映射验证完成');
        }

        // 全局错误处理
        window.addEventListener('error', function(event) {
            log(`❌ 全局错误: ${event.error.message}`);
        });
    </script>
</body>
</html>
