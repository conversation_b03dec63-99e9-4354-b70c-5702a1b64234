<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单系统健康检查测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px; 
            background-color: #f5f5f5;
        }
        .test-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .warning { background-color: #fff3cd; color: #856404; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .module-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .module-item {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f8f9fa;
        }
        .module-loaded { border-left: 4px solid #28a745; }
        .module-missing { border-left: 4px solid #dc3545; }
        .log-output {
            background: #2d3748;
            color: #ffffff;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .refresh-btn { background: #28a745; }
        .clear-btn { background: #6c757d; }
    </style>
</head>
<body>
    <h1>🚀 多订单系统健康检查测试</h1>
    <p>这个页面用于测试和验证多订单系统的健康状态，检查所有模块的加载情况。</p>

    <div class="test-card">
        <h2>📊 系统状态概览</h2>
        <div id="systemStatus" class="status info">正在初始化系统...</div>
        
        <div class="module-list" id="modulesList">
            <!-- 模块状态将动态填充 -->
        </div>

        <div>
            <button onclick="performHealthCheck()" class="refresh-btn">🔄 重新检查</button>
            <button onclick="clearLogs()" class="clear-btn">🗑️ 清除日志</button>
            <button onclick="exportReport()">📋 导出报告</button>
        </div>
    </div>

    <div class="test-card">
        <h2>📝 实时日志输出</h2>
        <div id="logOutput" class="log-output">等待日志输出...</div>
    </div>

    <div class="test-card">
        <h2>🔧 功能测试</h2>
        <button onclick="testMultiOrderDetection()">测试多订单检测</button>
        <button onclick="testFieldStandardization()">测试字段标准化</button>
        <button onclick="testBatchProcessing()">测试批量处理</button>
        <div id="testResults" class="status info" style="margin-top: 10px;">点击按钮开始功能测试</div>
    </div>

    <!-- 加载核心脚本 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <!-- ota-registry.js removed - merged into unified OTA system -->
    <!-- 🧹 架构简化：global-field-standardization-layer.js 已移除，功能已整合 -->
    
    <!-- 基础工具 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/gemini-service.js"></script>

    <!-- 多订单模块 -->
    <script src="js/multi-order/multi-order-state-manager.js"></script>
    <script src="js/multi-order/batch-processor.js"></script>
    <script src="js/multi-order/multi-order-detector.js"></script>
    <script src="js/multi-order/multi-order-processor.js"></script>
    <script src="js/multi-order/multi-order-renderer.js"></script>
    <script src="js/multi-order/multi-order-coordinator.js"></script>
    <script src="js/multi-order-manager-v2.js"></script>

    <script>
        // 全局日志捕获
        const logOutput = document.getElementById('logOutput');
        const originalConsoleLog = console.log;
        const originalConsoleWarn = console.warn;
        const originalConsoleError = console.error;

        function logToOutput(message, level = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = {
                'log': '📝',
                'warn': '⚠️',
                'error': '❌'
            }[level] || '📝';
            
            logOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            logToOutput(args.join(' '), 'log');
        };

        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            logToOutput(args.join(' '), 'warn');
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            logToOutput(args.join(' '), 'error');
        };

        // 系统健康检查
        function performHealthCheck() {
            const systemStatus = document.getElementById('systemStatus');
            const modulesList = document.getElementById('modulesList');

            console.log('开始执行系统健康检查...');

            const requiredModules = [
                { name: 'multiOrderCoordinator', displayName: '多订单协调器' },
                { name: 'multiOrderStateManager', displayName: '状态管理器' },
                { name: 'batchProcessor', displayName: '批量处理器' },
                { name: 'multiOrderDetector', displayName: '多订单检测器' },
                { name: 'multiOrderProcessor', displayName: '多订单处理器' },
                { name: 'multiOrderRenderer', displayName: '多订单渲染器' },
                { name: 'multiOrderManager', displayName: '多订单管理器V2' }
            ];

            let loadedCount = 0;
            let moduleHtml = '';

            requiredModules.forEach(module => {
                const isLoaded = !!(window.OTA && window.OTA[module.name]);
                const status = isLoaded ? 'loaded' : 'missing';
                const statusText = isLoaded ? '✅ 已加载' : '❌ 未加载';
                
                if (isLoaded) loadedCount++;

                moduleHtml += `
                    <div class="module-item module-${status}">
                        <strong>${module.displayName}</strong> (${module.name})<br>
                        <small>${statusText}</small>
                    </div>
                `;
            });

            modulesList.innerHTML = moduleHtml;

            // 更新总体状态
            const percentage = Math.round((loadedCount / requiredModules.length) * 100);
            if (percentage === 100) {
                systemStatus.className = 'status success';
                systemStatus.textContent = `✅ 系统健康 - 所有模块 (${loadedCount}/${requiredModules.length}) 已正确加载`;
            } else if (percentage >= 70) {
                systemStatus.className = 'status warning';
                systemStatus.textContent = `⚠️ 部分模块缺失 - ${loadedCount}/${requiredModules.length} 个模块已加载 (${percentage}%)`;
            } else {
                systemStatus.className = 'status error';
                systemStatus.textContent = `❌ 系统异常 - 仅 ${loadedCount}/${requiredModules.length} 个模块已加载 (${percentage}%)`;
            }

            console.log(`健康检查完成: ${loadedCount}/${requiredModules.length} 模块已加载`);
        }

        // 功能测试
        function testMultiOrderDetection() {
            const testResults = document.getElementById('testResults');
            console.log('开始测试多订单检测功能...');

            try {
                if (window.OTA && window.OTA.multiOrderManager && 
                    typeof window.OTA.multiOrderManager.analyzeInputForMultiOrder === 'function') {
                    
                    testResults.className = 'status success';
                    testResults.textContent = '✅ 多订单检测功能可用';
                    console.log('多订单检测功能测试通过');
                } else {
                    testResults.className = 'status error';
                    testResults.textContent = '❌ 多订单检测功能不可用';
                    console.error('多订单检测功能测试失败');
                }
            } catch (error) {
                testResults.className = 'status error';
                testResults.textContent = `❌ 测试出错: ${error.message}`;
                console.error('多订单检测测试异常:', error);
            }
        }

        function testFieldStandardization() {
            const testResults = document.getElementById('testResults');
            console.log('开始测试字段标准化功能...');

            try {
                if (window.standardizeFieldsToApi && typeof window.standardizeFieldsToApi === 'function') {
                    const testData = {
                        customerName: '测试客户',
                        pickupLocation: '测试地点',
                        carType: '测试车型'
                    };
                    
                    const standardized = window.standardizeFieldsToApi(testData, 'test');
                    
                    if (standardized.customer_name === '测试客户' && 
                        standardized.pickup === '测试地点' && 
                        standardized.car_type_id === '测试车型') {
                        
                        testResults.className = 'status success';
                        testResults.textContent = '✅ 字段标准化功能正常';
                        console.log('字段标准化测试通过:', standardized);
                    } else {
                        testResults.className = 'status warning';
                        testResults.textContent = '⚠️ 字段标准化结果异常';
                        console.warn('字段标准化结果:', standardized);
                    }
                } else {
                    testResults.className = 'status error';
                    testResults.textContent = '❌ 字段标准化功能不可用';
                    console.error('字段标准化功能不存在');
                }
            } catch (error) {
                testResults.className = 'status error';
                testResults.textContent = `❌ 测试出错: ${error.message}`;
                console.error('字段标准化测试异常:', error);
            }
        }

        function testBatchProcessing() {
            const testResults = document.getElementById('testResults');
            console.log('开始测试批量处理功能...');

            try {
                if (window.OTA && window.OTA.batchProcessor && 
                    typeof window.OTA.batchProcessor.processBatch === 'function') {
                    
                    testResults.className = 'status success';
                    testResults.textContent = '✅ 批量处理功能可用';
                    console.log('批量处理功能测试通过');
                } else {
                    testResults.className = 'status error';
                    testResults.textContent = '❌ 批量处理功能不可用';
                    console.error('批量处理功能测试失败');
                }
            } catch (error) {
                testResults.className = 'status error';
                testResults.textContent = `❌ 测试出错: ${error.message}`;
                console.error('批量处理测试异常:', error);
            }
        }

        function clearLogs() {
            logOutput.textContent = '日志已清除...\n';
        }

        function exportReport() {
            const report = {
                timestamp: new Date().toISOString(),
                modules: {},
                logs: logOutput.textContent
            };

            const requiredModules = [
                'multiOrderCoordinator', 'multiOrderStateManager', 'batchProcessor',
                'multiOrderDetector', 'multiOrderProcessor', 'multiOrderRenderer', 'multiOrderManager'
            ];

            requiredModules.forEach(moduleName => {
                report.modules[moduleName] = !!(window.OTA && window.OTA[moduleName]);
            });

            const reportJson = JSON.stringify(report, null, 2);
            const blob = new Blob([reportJson], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `multi-order-health-report-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);

            console.log('健康报告已导出');
        }

        // 页面加载完成后自动执行健康检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('页面加载完成，开始自动健康检查...');
                performHealthCheck();
            }, 3000); // 给模块3秒时间加载
        });

        // 监听控制台输出的特殊事件
        window.addEventListener('unhandledrejection', event => {
            console.error('未处理的Promise拒绝:', event.reason);
        });

        window.addEventListener('error', event => {
            console.error('全局错误:', event.error?.message || event.message);
        });
    </script>
</body>
</html>
