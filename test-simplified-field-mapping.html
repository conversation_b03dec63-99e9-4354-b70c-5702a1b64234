<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化字段映射测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .form-field {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        input, select {
            width: 200px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        #results {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 简化字段映射测试</h1>
        
        <div class="test-section">
            <h3>📝 模拟表单字段</h3>
            <div class="form-field">
                <label>客户姓名:</label>
                <input type="text" id="customerName" />
            </div>
            <div class="form-field">
                <label>接送时间:</label>
                <input type="time" id="pickupTime" />
            </div>
            <div class="form-field">
                <label>接送日期:</label>
                <input type="date" id="pickupDate" />
            </div>
            <div class="form-field">
                <label>接送地点:</label>
                <input type="text" id="pickup" />
            </div>
            <div class="form-field">
                <label>目的地:</label>
                <input type="text" id="dropoff" />
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 测试操作</h3>
            <button onclick="testSimplifiedMapping()">测试简化字段映射</button>
            <button onclick="testTimeFieldPriority()">测试时间字段优先级</button>
            <button onclick="clearAll()">清空所有</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        let results = document.getElementById('results');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : '';
            const logEntry = `[${timestamp}] ${message}`;
            
            const span = document.createElement('span');
            span.className = className;
            span.textContent = logEntry + '\n';
            results.appendChild(span);
            results.scrollTop = results.scrollHeight;
        }

        function clearAll() {
            results.innerHTML = '';
            document.querySelectorAll('input').forEach(input => input.value = '');
        }

        function testSimplifiedMapping() {
            log('🧪 开始测试简化字段映射');
            
            // 模拟从Gemini返回的数据（不再被全局拦截器处理）
            const geminiData = {
                "customer_name": "梁嘉依",
                "customer_contact": "18057562938",
                "pickup": "斗湖机场MAIN",
                "destination": "永达大酒店",
                "date": "2025-08-15",
                "arrival_time": "19:00", // 关键测试字段
                "departure_time": null,
                "ota_price": 136.98,
                "currency": "MYR"
            };
            
            log('📥 模拟Gemini数据接收');
            
            // 使用简化的字段映射表（直接在FormManager中）
            const fieldMapping = {
                'customer_name': 'customerName',
                'pickup': 'pickup',
                'destination': 'dropoff',
                'date': 'pickupDate',
                'arrival_time': 'pickupTime', // 关键映射
                'departure_time': 'pickupTime',
                'time': 'pickupTime'
            };
            
            log('🔄 开始字段映射处理');
            
            let mappingResults = {};
            let successCount = 0;
            
            for (const [sourceField, targetField] of Object.entries(fieldMapping)) {
                if (geminiData.hasOwnProperty(sourceField)) {
                    const value = geminiData[sourceField];
                    if (value !== null && value !== undefined) {
                        mappingResults[targetField] = value;
                        
                        // 实际更新表单字段
                        const element = document.getElementById(targetField);
                        if (element) {
                            element.value = value;
                            log(`✅ ${sourceField} → ${targetField} = ${value}`, 'success');
                            successCount++;
                        } else {
                            log(`⚠️ 表单元素 ${targetField} 不存在`, 'error');
                        }
                    } else {
                        log(`⏭️ 跳过空值字段: ${sourceField}`);
                    }
                }
            }
            
            log(`🎯 映射完成，成功 ${successCount} 个字段`, 'success');
            
            // 验证关键字段
            if (mappingResults.pickupTime === "19:00") {
                log('✅ 关键验证通过：arrival_time 正确映射到 pickupTime', 'success');
            } else {
                log('❌ 关键验证失败：arrival_time 映射异常', 'error');
            }
        }

        function testTimeFieldPriority() {
            log('⏰ 测试时间字段优先级');
            
            // 测试多个时间字段同时存在时的优先级处理
            const testScenarios = [
                {
                    name: "arrival_time 优先",
                    data: {
                        "arrival_time": "19:00",
                        "departure_time": "10:00",
                        "time": "12:00"
                    },
                    expected: "19:00"
                },
                {
                    name: "pickup_time 最高优先级",
                    data: {
                        "pickup_time": "15:00",
                        "arrival_time": "19:00",
                        "time": "12:00"
                    },
                    expected: "15:00"
                },
                {
                    name: "只有 departure_time",
                    data: {
                        "departure_time": "08:30"
                    },
                    expected: "08:30"
                }
            ];
            
            testScenarios.forEach((scenario, index) => {
                log(`📋 场景 ${index + 1}: ${scenario.name}`);
                
                // 模拟优先级处理逻辑
                const priority = {
                    'pickup_time': 1,
                    'arrival_time': 2, 
                    'departure_time': 3,
                    'time': 4
                };
                
                let selectedTime = null;
                let highestPriority = 999;
                
                for (const [field, value] of Object.entries(scenario.data)) {
                    if (value && priority[field] < highestPriority) {
                        selectedTime = value;
                        highestPriority = priority[field];
                    }
                }
                
                if (selectedTime === scenario.expected) {
                    log(`  ✅ 优先级处理正确: ${selectedTime}`, 'success');
                    
                    // 更新表单显示结果
                    const timeElement = document.getElementById('pickupTime');
                    if (timeElement) {
                        timeElement.value = selectedTime;
                    }
                } else {
                    log(`  ❌ 优先级处理错误: 期望 ${scenario.expected}, 实际 ${selectedTime}`, 'error');
                }
            });
        }

        // 页面加载时显示初始信息
        window.onload = function() {
            log('🚀 简化字段映射测试已加载');
            log('📝 这个测试验证多层架构清理后的字段映射功能');
            log('🎯 重点验证 arrival_time 是否能正确映射到 pickupTime');
        };
    </script>
</body>
</html>