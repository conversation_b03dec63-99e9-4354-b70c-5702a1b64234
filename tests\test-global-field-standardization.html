<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全局字段标准化测试验证</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .json-display {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        .statistics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .stat-card {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
        }
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .data-flow-visual {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 20px 0;
            padding: 15px;
            background: linear-gradient(90deg, #e3f2fd 0%, #f3e5f5 100%);
            border-radius: 10px;
        }
        .flow-step {
            text-align: center;
            flex: 1;
        }
        .flow-arrow {
            font-size: 20px;
            color: #666;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 全局字段标准化系统测试验证</h1>
        <p>验证数据流全流程字段名称一致性 - GoMyHire API标准规范</p>
    </div>

    <div class="test-section">
        <h2 class="test-title">📊 系统状态检查</h2>
        <div id="systemStatus"></div>
        
        <button onclick="checkSystemStatus()">检查系统状态</button>
        <button onclick="runAllTests()">运行完整测试套件</button>
        <button onclick="generateReport()">生成测试报告</button>
    </div>

    <div class="test-section">
        <h2 class="test-title">🔄 数据流验证</h2>
        <div class="data-flow-visual">
            <div class="flow-step">
                <strong>Gemini输出</strong><br>
                <small>混合字段格式</small>
            </div>
            <div class="flow-arrow">→</div>
            <div class="flow-step">
                <strong>字段标准化</strong><br>
                <small>统一映射处理</small>
            </div>
            <div class="flow-arrow">→</div>
            <div class="flow-step">
                <strong>API发送</strong><br>
                <small>标准snake_case</small>
            </div>
        </div>
        <div id="dataFlowResults"></div>
        <button onclick="testDataFlow()">测试数据流一致性</button>
    </div>

    <div class="test-section">
        <h2 class="test-title">🎯 字段映射测试</h2>
        <div id="fieldMappingResults"></div>
        <button onclick="testFieldMappings()">测试字段映射</button>
        <button onclick="testEdgeCases()">测试边缘情况</button>
    </div>

    <div class="test-section">
        <h2 class="test-title">📈 性能统计</h2>
        <div id="performanceStats" class="statistics-grid"></div>
        <button onclick="loadPerformanceStats()">刷新统计</button>
        <button onclick="clearCache()">清理缓存</button>
    </div>

    <!-- 加载必要的依赖 -->
    <!-- 🧹 架构简化：global-field-standardization-layer.js 已移除，功能已整合 -->
    
    <script>
        let testResults = [];
        
        // 检查系统状态
        function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            statusDiv.innerHTML = '';
            
            const checks = [
                {
                    name: '字段标准化层',
                    test: () => window.OTA && window.OTA.globalFieldStandardizationLayer,
                    description: '全局字段标准化拦截层已加载'
                },
                {
                    name: '标准化方法',
                    test: () => typeof window.standardizeFieldsToApi === 'function',
                    description: '全局标准化方法可用'
                },
                {
                    name: '映射配置',
                    test: () => {
                        const layer = window.OTA.globalFieldStandardizationLayer;
                        return layer && layer.UNIFIED_FIELD_MAPPING && Object.keys(layer.UNIFIED_FIELD_MAPPING).length > 0;
                    },
                    description: '字段映射配置已加载'
                },
                {
                    name: 'API标准字段',
                    test: () => {
                        const layer = window.OTA.globalFieldStandardizationLayer;
                        return layer && layer.API_STANDARD_FIELDS && Object.keys(layer.API_STANDARD_FIELDS).length > 0;
                    },
                    description: 'API标准字段定义已加载'
                }
            ];
            
            checks.forEach(check => {
                const result = check.test();
                const resultDiv = document.createElement('div');
                resultDiv.className = `test-result ${result ? 'success' : 'error'}`;
                resultDiv.innerHTML = `
                    <strong>${check.name}</strong>: ${result ? '✅ 通过' : '❌ 失败'}
                    <br><small>${check.description}</small>
                `;
                statusDiv.appendChild(resultDiv);
            });
        }

        // 测试数据流一致性
        function testDataFlow() {
            const resultsDiv = document.getElementById('dataFlowResults');
            resultsDiv.innerHTML = '';
            
            // 模拟不同来源的数据格式
            const testDataSources = {
                'Gemini输出数据': {
                    customerName: '张三',
                    carTypeId: 5,
                    pickupDate: '2025-01-08',
                    pickupTime: '10:00',
                    otaPrice: '150.00'
                },
                '表单输入数据': {
                    pickup_location: 'KLIA机场',
                    dropoff_location: '吉隆坡市中心',
                    passenger_count: 3,
                    luggage_count: 2
                },
                '历史遗留数据': {
                    drivingRegionId: 1,
                    subCategoryId: 2,
                    extraRequirement: '需要婴儿座椅'
                }
            };
            
            Object.entries(testDataSources).forEach(([sourceName, sourceData]) => {
                const resultDiv = document.createElement('div');
                
                try {
                    // 测试标准化处理
                    const standardized = window.standardizeFieldsToApi(sourceData, sourceName);
                    
                    // 验证转换结果
                    const hasApiStandardFields = Object.keys(standardized).every(field => {
                        const layer = window.OTA.globalFieldStandardizationLayer;
                        return layer.API_STANDARD_FIELDS.hasOwnProperty(field) || 
                               layer.IGNORABLE_FIELDS.has(field);
                    });
                    
                    resultDiv.className = `test-result ${hasApiStandardFields ? 'success' : 'warning'}`;
                    resultDiv.innerHTML = `
                        <h4>${sourceName} ${hasApiStandardFields ? '✅' : '⚠️'}</h4>
                        <div class="json-display">
                            <strong>原始数据:</strong><br>
                            ${JSON.stringify(sourceData, null, 2)}
                            <br><br>
                            <strong>标准化后:</strong><br>
                            ${JSON.stringify(standardized, null, 2)}
                        </div>
                    `;
                    
                } catch (error) {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `
                        <h4>${sourceName} ❌</h4>
                        <p>标准化失败: ${error.message}</p>
                    `;
                }
                
                resultsDiv.appendChild(resultDiv);
            });
        }

        // 测试字段映射
        function testFieldMappings() {
            const resultsDiv = document.getElementById('fieldMappingResults');
            resultsDiv.innerHTML = '';
            
            const layer = window.OTA.globalFieldStandardizationLayer;
            if (!layer) {
                resultsDiv.innerHTML = '<div class="test-result error">字段标准化层未加载</div>';
                return;
            }
            
            // 测试关键字段映射
            const criticalFieldTests = [
                { input: 'carTypeId', expected: 'car_type_id', category: '车型字段' },
                { input: 'drivingRegionId', expected: 'driving_region_id', category: '区域字段' },
                { input: 'customerName', expected: 'customer_name', category: '客户信息' },
                { input: 'pickupDate', expected: 'date', category: '时间字段' },
                { input: 'otaPrice', expected: 'ota_price', category: '价格字段' },
                { input: 'pickup_location', expected: 'pickup', category: '位置字段' }
            ];
            
            let passedTests = 0;
            
            criticalFieldTests.forEach(test => {
                const testData = { [test.input]: 'test_value' };
                const result = layer.standardizeToApiFields(testData, 'field-test');
                const actualOutput = Object.keys(result)[0];
                const passed = actualOutput === test.expected;
                
                if (passed) passedTests++;
                
                const resultDiv = document.createElement('div');
                resultDiv.className = `test-result ${passed ? 'success' : 'error'}`;
                resultDiv.innerHTML = `
                    <strong>${test.category}</strong>: ${test.input} → ${actualOutput} 
                    ${passed ? '✅' : '❌'} (期望: ${test.expected})
                `;
                resultsDiv.appendChild(resultDiv);
            });
            
            // 显示总体结果
            const summaryDiv = document.createElement('div');
            summaryDiv.className = `test-result ${passedTests === criticalFieldTests.length ? 'success' : 'warning'}`;
            summaryDiv.innerHTML = `
                <h4>字段映射测试总结</h4>
                <p>通过率: ${passedTests}/${criticalFieldTests.length} (${Math.round(passedTests/criticalFieldTests.length*100)}%)</p>
            `;
            resultsDiv.insertBefore(summaryDiv, resultsDiv.firstChild);
        }

        // 测试边缘情况
        function testEdgeCases() {
            const resultsDiv = document.getElementById('fieldMappingResults');
            
            const edgeCases = [
                {
                    name: '空对象处理',
                    input: {},
                    test: (result) => typeof result === 'object' && Object.keys(result).length === 0
                },
                {
                    name: '未知字段处理',
                    input: { unknownField: 'test', validField: 'test' },
                    test: (result) => result.hasOwnProperty('unknownField')
                },
                {
                    name: '已标准字段保持不变',
                    input: { car_type_id: 5, customer_name: 'test' },
                    test: (result) => result.car_type_id === 5 && result.customer_name === 'test'
                },
                {
                    name: '忽略字段测试',
                    input: { _otaChannel: 'test', car_type_id: 5 },
                    test: (result) => !result.hasOwnProperty('_otaChannel') && result.hasOwnProperty('car_type_id')
                }
            ];
            
            edgeCases.forEach(testCase => {
                try {
                    const result = window.standardizeFieldsToApi(testCase.input, 'edge-case');
                    const passed = testCase.test(result);
                    
                    const resultDiv = document.createElement('div');
                    resultDiv.className = `test-result ${passed ? 'success' : 'error'}`;
                    resultDiv.innerHTML = `
                        <strong>${testCase.name}</strong>: ${passed ? '✅ 通过' : '❌ 失败'}
                    `;
                    resultsDiv.appendChild(resultDiv);
                    
                } catch (error) {
                    const resultDiv = document.createElement('div');
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `
                        <strong>${testCase.name}</strong>: ❌ 异常 - ${error.message}
                    `;
                    resultsDiv.appendChild(resultDiv);
                }
            });
        }

        // 加载性能统计
        function loadPerformanceStats() {
            const statsDiv = document.getElementById('performanceStats');
            const layer = window.OTA.globalFieldStandardizationLayer;
            
            if (!layer) {
                statsDiv.innerHTML = '<div class="error">字段标准化层未加载</div>';
                return;
            }
            
            const stats = layer.getStatistics();
            
            const statCards = [
                { label: '字段转换次数', value: stats.transformations },
                { label: '缓存命中次数', value: stats.cacheHits },
                { label: '错误次数', value: stats.errors },
                { label: '缓存大小', value: stats.cacheSize },
                { label: '映射规则数', value: stats.totalMappings },
                { label: 'API标准字段数', value: stats.apiStandardFields }
            ];
            
            statsDiv.innerHTML = statCards.map(stat => `
                <div class="stat-card">
                    <div class="stat-number">${stat.value}</div>
                    <div class="stat-label">${stat.label}</div>
                </div>
            `).join('');
        }

        // 清理缓存
        function clearCache() {
            const layer = window.OTA.globalFieldStandardizationLayer;
            if (layer) {
                layer.clearCache();
                alert('缓存已清理');
                loadPerformanceStats();
            }
        }

        // 运行所有测试
        function runAllTests() {
            console.log('🧪 开始运行完整测试套件...');
            
            checkSystemStatus();
            setTimeout(() => {
                testDataFlow();
                setTimeout(() => {
                    testFieldMappings();
                    setTimeout(() => {
                        testEdgeCases();
                        setTimeout(() => {
                            loadPerformanceStats();
                            console.log('✅ 测试套件执行完成');
                        }, 500);
                    }, 500);
                }, 500);
            }, 500);
        }

        // 生成测试报告
        function generateReport() {
            const layer = window.OTA.globalFieldStandardizationLayer;
            if (!layer) {
                alert('字段标准化层未加载，无法生成报告');
                return;
            }
            
            const report = layer.generateMappingReport();
            
            // 创建报告窗口
            const reportWindow = window.open('', '_blank');
            reportWindow.document.write(`
                <html>
                <head>
                    <title>字段标准化系统报告</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; }
                        .report-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
                        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; }
                    </style>
                </head>
                <body>
                    <h1>🔧 全局字段标准化系统报告</h1>
                    <div class="report-section">
                        <h2>📊 统计摘要</h2>
                        <pre>${JSON.stringify(report.summary, null, 2)}</pre>
                    </div>
                    <div class="report-section">
                        <h2>🗺️ 字段映射概况</h2>
                        <pre>${JSON.stringify(report.fieldMappings, null, 2)}</pre>
                    </div>
                    <div class="report-section">
                        <h2>🔄 活跃映射规则</h2>
                        <pre>${JSON.stringify(report.activeMappings, null, 2)}</pre>
                    </div>
                    <div class="report-section">
                        <p><strong>报告生成时间:</strong> ${report.generatedAt}</p>
                    </div>
                </body>
                </html>
            `);
        }

        // 页面加载时自动检查系统状态
        window.addEventListener('load', function() {
            console.log('🔧 字段标准化测试页面已加载');
            
            // 延迟检查确保所有脚本已加载
            setTimeout(() => {
                checkSystemStatus();
                loadPerformanceStats();
            }, 1000);
        });

    </script>
</body>
</html>