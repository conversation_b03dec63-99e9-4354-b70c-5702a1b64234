<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间映射调试测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        #results {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
        }
        .form-field {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        input, select {
            width: 200px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 时间映射调试测试</h1>
        
        <div class="test-section">
            <h3>🎯 测试目标</h3>
            <p>调试为什么Gemini返回的arrival_time: "19:00"在表单映射时显示为null</p>
        </div>

        <div class="test-section">
            <h3>📝 模拟表单字段</h3>
            <div class="form-field">
                <label>客户姓名:</label>
                <input type="text" id="customerName" />
            </div>
            <div class="form-field">
                <label>接送时间:</label>
                <input type="time" id="pickupTime" />
            </div>
            <div class="form-field">
                <label>接送日期:</label>
                <input type="date" id="pickupDate" />
            </div>
            <div class="form-field">
                <label>接送地点:</label>
                <input type="text" id="pickup" />
            </div>
            <div class="form-field">
                <label>目的地:</label>
                <input type="text" id="dropoff" />
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 测试操作</h3>
            <button onclick="testGeminiData()">测试Gemini原始数据</button>
            <button onclick="testFormManagerDirect()">直接测试FormManager</button>
            <button onclick="testTimeFieldMapping()">测试时间字段映射</button>
            <button onclick="clearResults()">清空结果</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        let results = document.getElementById('results');
        
        function log(message, data = null) {
            const timestamp = new Date().toLocaleTimeString();
            let logEntry = `[${timestamp}] ${message}`;
            if (data !== null) {
                logEntry += `\n    数据: ${JSON.stringify(data, null, 2)}`;
            }
            results.textContent += logEntry + '\n\n';
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            results.textContent = '';
        }

        // 模拟Gemini返回的原始数据
        const geminiData = {
            "customer_name": "梁嘉依",
            "customer_contact": "18057562938",
            "customer_email": null,
            "ota": "Fliggy",
            "ota_reference_number": "4676692140284827120",
            "flight_info": "AK5748",
            "departure_time": null,
            "arrival_time": "19:00",
            "flight_type": "Arrival",
            "date": "2025-08-15",
            "time": null,
            "pickup": "斗湖机场MAIN",
            "destination": "永达大酒店",
            "passenger_number": 4,
            "luggage_number": null,
            "sub_category_id": 2,
            "car_type_id": 5,
            "driving_region_id": 4,
            "baby_chair": null,
            "tour_guide": null,
            "meet_and_greet": null,
            "needs_paging_service": null,
            "ota_price": 136.98,
            "currency": "MYR",
            "extra_requirement": null
        };

        function testGeminiData() {
            log('🔍 测试Gemini原始数据');
            log('Gemini返回的数据:', geminiData);
            
            // 检查关键时间字段
            log('关键时间字段检查:');
            log('  departure_time', geminiData.departure_time);
            log('  arrival_time', geminiData.arrival_time);
            log('  time', geminiData.time);
            log('  date', geminiData.date);
        }

        function testFormManagerDirect() {
            log('🔧 直接测试表单字段映射');
            
            // 模拟FormManager的字段映射
            const fieldMapping = {
                'customer_name': 'customerName',
                'pickup': 'pickup',
                'destination': 'dropoff', 
                'dropoff_location': 'dropoff',
                'pickup_date': 'pickupDate',
                'time': 'pickupTime',
                'arrival_time': 'pickupTime',
                'departure_time': 'pickupTime',
                'pickup_time': 'pickupTime'
            };

            log('字段映射表:', fieldMapping);
            
            // 模拟字段映射处理
            for (const snakeCaseKey in geminiData) {
                if (fieldMapping[snakeCaseKey]) {
                    const elementKey = fieldMapping[snakeCaseKey];
                    const element = document.getElementById(elementKey);
                    const value = geminiData[snakeCaseKey];
                    
                    log(`字段映射: ${snakeCaseKey} → ${elementKey}`, {
                        value: value,
                        hasElement: !!element,
                        valueType: typeof value,
                        isNull: value === null,
                        isEmpty: value === ''
                    });
                    
                    // 实际设置字段值
                    if (element && value !== null && value !== undefined) {
                        if (element.type === 'time' && value) {
                            element.value = value;
                            log(`  ✅ 时间字段设置成功: ${value}`);
                        } else if (element.type === 'date' && value) {
                            element.value = value;
                            log(`  ✅ 日期字段设置成功: ${value}`);
                        } else if (element.type !== 'time' && element.type !== 'date') {
                            element.value = value;
                            log(`  ✅ 普通字段设置成功: ${value}`);
                        }
                    }
                }
            }
        }

        function testTimeFieldMapping() {
            log('⏰ 专门测试时间字段映射');
            
            const timeFields = ['departure_time', 'arrival_time', 'time'];
            const pickupTimeElement = document.getElementById('pickupTime');
            
            log('pickup时间元素状态:', {
                exists: !!pickupTimeElement,
                type: pickupTimeElement?.type,
                currentValue: pickupTimeElement?.value
            });
            
            for (const field of timeFields) {
                const value = geminiData[field];
                log(`处理时间字段: ${field}`, {
                    value: value,
                    isNull: value === null,
                    isUndefined: value === undefined,
                    isEmpty: value === '',
                    type: typeof value
                });
                
                // 模拟FormManager的时间字段处理逻辑
                if (pickupTimeElement && pickupTimeElement.type === 'time' && value) {
                    const isPrimary = field === 'pickup_time';
                    const shouldUpdate = isPrimary || !pickupTimeElement.value;
                    
                    log(`  时间字段更新检查:`, {
                        isPrimary: isPrimary,
                        currentElementValue: pickupTimeElement.value,
                        shouldUpdate: shouldUpdate
                    });
                    
                    if (shouldUpdate) {
                        pickupTimeElement.value = value;
                        log(`  ✅ 时间字段已更新: ${field} = ${value}`);
                    } else {
                        log(`  ⏭️ 跳过更新: 字段非主要且元素已有值`);
                    }
                }
            }
            
            log('最终pickup时间元素值:', pickupTimeElement?.value);
        }

        // 页面加载时显示初始信息
        window.onload = function() {
            log('🚀 时间映射调试测试已加载');
            log('可以开始测试了...');
        };
    </script>
</body>
</html>