<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化加载逻辑测试 - 方案A验证</title>
    <style>
        body {
            font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .status {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
        }
        .success { background: rgba(76, 175, 80, 0.3); border-left: 4px solid #4CAF50; }
        .error { background: rgba(244, 67, 54, 0.3); border-left: 4px solid #f44336; }
        .warning { background: rgba(255, 152, 0, 0.3); border-left: 4px solid #FF9800; }
        .info { background: rgba(33, 150, 243, 0.3); border-left: 4px solid #2196F3; }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-weight: 500;
        }
        button:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
        }
        .metric-label {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 方案A: 重新分配加载阶段 - 验证测试</h1>
        <p>验证优化版本2.0的5阶段加载架构是否成功消除警告并提升性能</p>

        <div class="test-section">
            <h3>📊 性能指标监控</h3>
            <div class="metrics" id="metricsContainer">
                <div class="metric">
                    <div class="metric-value" id="loadTime">-</div>
                    <div class="metric-label">总加载时间 (ms)</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="warningCount">-</div>
                    <div class="metric-label">启动警告数量</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="phaseCount">-</div>
                    <div class="metric-label">加载阶段数</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="registrations">-</div>
                    <div class="metric-label">服务注册数</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 启动过程验证</h3>
            <button onclick="testStartupProcess()">验证启动过程</button>
            <button onclick="testFormManagerRegistration()">测试FormManager注册</button>
            <button onclick="testVehicleConfigIntegration()">测试集成验证</button>
            <button onclick="clearLogs()">清空日志</button>
            <div id="startupResults" style="max-height: 300px; overflow-y: auto; margin-top: 10px;"></div>
        </div>

        <div class="test-section">
            <h3>📋 系统状态检查</h3>
            <button onclick="checkSystemHealth()">检查系统健康状态</button>
            <button onclick="validateArchitecture()">验证架构优化</button>
            <button onclick="measurePerformance()">性能基准测试</button>
            <div id="healthResults" style="max-height: 300px; overflow-y: auto; margin-top: 10px;"></div>
        </div>

        <div class="test-section">
            <h3>🎯 预期结果对比</h3>
            <div id="comparisonResults">
                <div class="status info">等待测试结果...</div>
            </div>
        </div>
    </div>

    <!-- 加载优化后的script-manifest -->
    <script src="js/core/script-manifest.js"></script>

    <script>
        let startTime = performance.now();
        let warnings = [];
        let errors = [];

        // 监控控制台输出
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;

        console.warn = function(...args) {
            warnings.push(args.join(' '));
            originalWarn.apply(console, args);
        };

        console.error = function(...args) {
            errors.push(args.join(' '));
            originalError.apply(console, args);
        };

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(statusDiv);
            container.scrollTop = container.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('startupResults').innerHTML = '';
            document.getElementById('healthResults').innerHTML = '';
            warnings = [];
            errors = [];
        }

        function updateMetrics() {
            const loadTime = performance.now() - startTime;
            document.getElementById('loadTime').textContent = Math.round(loadTime);
            document.getElementById('warningCount').textContent = warnings.length;
            
            if (window.OTA?.scriptManifest?.phases) {
                document.getElementById('phaseCount').textContent = window.OTA.scriptManifest.phases.length;
            }

            if (window.OTA?.container?.services) {
                document.getElementById('registrations').textContent = window.OTA.container.services.size || 0;
            }
        }

        function testStartupProcess() {
            addResult('startupResults', '开始启动过程验证...', 'info');
            
            // 检查Script Manifest
            if (window.OTA?.scriptManifest) {
                const manifest = window.OTA.scriptManifest;
                addResult('startupResults', `✅ Script Manifest v${manifest.version} 已加载`, 'success');
                addResult('startupResults', `📊 包含 ${manifest.phases.length} 个加载阶段`, 'info');
                
                // 检查阶段名称
                const expectedPhases = ['infrastructure', 'configuration', 'services', 'managers', 'launch'];
                manifest.phases.forEach((phase, index) => {
                    if (phase.name === expectedPhases[index]) {
                        addResult('startupResults', `✅ 阶段${index+1}: ${phase.name} - 正确`, 'success');
                    } else {
                        addResult('startupResults', `❌ 阶段${index+1}: 预期${expectedPhases[index]}，实际${phase.name}`, 'error');
                    }
                });
            } else {
                addResult('startupResults', '❌ Script Manifest 未加载', 'error');
            }

            updateMetrics();
        }

        function testFormManagerRegistration() {
            addResult('startupResults', '测试FormManager注册状态...', 'info');
            
            // 检查FormManager类是否存在
            if (window.OTA?.managers?.FormManager) {
                addResult('startupResults', '✅ FormManager类已定义', 'success');
            } else {
                addResult('startupResults', '❌ FormManager类未找到', 'error');
            }

            // 检查依赖容器中的注册
            if (window.OTA?.container) {
                const container = window.OTA.container;
                let registrationCount = 0;
                
                if (container.services?.has('formManagerClass')) {
                    addResult('startupResults', '✅ formManagerClass 已注册', 'success');
                    registrationCount++;
                }
                if (container.services?.has('formManager')) {
                    addResult('startupResults', '✅ formManager 实例已注册', 'success');  
                    registrationCount++;
                }

                if (registrationCount === 0) {
                    addResult('startupResults', '⚠️ FormManager注册将在后续阶段完成', 'warning');
                }
            }

            // 检查重复注册警告
            const formManagerWarnings = warnings.filter(w => w.includes('formManager') && w.includes('已存在'));
            if (formManagerWarnings.length === 0) {
                addResult('startupResults', '✅ 无FormManager重复注册警告', 'success');
            } else {
                addResult('startupResults', `❌ 检测到 ${formManagerWarnings.length} 个重复注册警告`, 'error');
            }
        }

        function testVehicleConfigIntegration() {
            addResult('startupResults', '测试VehicleConfigIntegration状态...', 'info');
            
            if (window.getVehicleConfigIntegration) {
                const integration = window.getVehicleConfigIntegration();
                if (integration) {
                    addResult('startupResults', '✅ VehicleConfigIntegration已加载', 'success');
                    
                    // 检查是否有早期执行的集成警告
                    const integrationWarnings = warnings.filter(w => 
                        w.includes('VehicleConfigIntegration') && w.includes('集成问题')
                    );
                    
                    if (integrationWarnings.length === 0) {
                        addResult('startupResults', '✅ 无早期集成验证警告', 'success');
                    } else {
                        addResult('startupResults', `⚠️ 仍有 ${integrationWarnings.length} 个集成警告`, 'warning');
                    }

                    addResult('startupResults', '💡 集成验证已设置为延迟执行', 'info');
                } else {
                    addResult('startupResults', '❌ VehicleConfigIntegration未初始化', 'error');
                }
            } else {
                addResult('startupResults', '❌ VehicleConfigIntegration访问函数未找到', 'error');
            }
        }

        function checkSystemHealth() {
            addResult('healthResults', '开始系统健康检查...', 'info');
            
            // 检查总体错误和警告
            addResult('healthResults', `📊 总警告数: ${warnings.length}`, warnings.length === 0 ? 'success' : 'warning');
            addResult('healthResults', `📊 总错误数: ${errors.length}`, errors.length === 0 ? 'success' : 'error');

            // 检查关键服务
            const criticalServices = [
                'OTA命名空间',
                'ScriptManifest',
                'DependencyContainer'
            ];

            criticalServices.forEach(service => {
                let exists = false;
                switch(service) {
                    case 'OTA命名空间':
                        exists = !!window.OTA;
                        break;
                    case 'ScriptManifest':
                        exists = !!(window.OTA?.scriptManifest);
                        break;
                    case 'DependencyContainer':
                        exists = !!(window.OTA?.container);
                        break;
                }
                
                addResult('healthResults', 
                    exists ? `✅ ${service} 正常` : `❌ ${service} 缺失`, 
                    exists ? 'success' : 'error'
                );
            });

            updateMetrics();
        }

        function validateArchitecture() {
            addResult('healthResults', '验证架构优化效果...', 'info');
            
            const expectedOptimizations = [
                'FormManager类定义和实例化分离',
                'VehicleConfigIntegration延迟执行',
                '5阶段清晰架构分层',
                '消除重复注册警告',
                '启动性能提升15-20%'
            ];

            if (window.OTA?.scriptManifest?.optimizations) {
                const actualOptimizations = window.OTA.scriptManifest.optimizations;
                expectedOptimizations.forEach(opt => {
                    if (actualOptimizations.includes(opt)) {
                        addResult('healthResults', `✅ ${opt}`, 'success');
                    } else {
                        addResult('healthResults', `❌ 缺少优化: ${opt}`, 'error');
                    }
                });
            }

            // 验证分离效果
            const formManagerInPhase2 = window.OTA?.scriptManifest?.phases?.[1]?.scripts?.includes('js/managers/form-manager.js');
            const uiManagerInPhase5 = window.OTA?.scriptManifest?.phases?.[4]?.scripts?.includes('js/ui-manager.js');
            
            if (formManagerInPhase2) {
                addResult('healthResults', '✅ FormManager在阶段2正确加载', 'success');
            }
            if (uiManagerInPhase5) {
                addResult('healthResults', '✅ UIManager在阶段5正确加载', 'success');
            }
        }

        function measurePerformance() {
            addResult('healthResults', '开始性能基准测试...', 'info');
            
            const currentTime = performance.now() - startTime;
            addResult('healthResults', `⏱️ 当前加载时间: ${Math.round(currentTime)}ms`, 'info');
            
            // 简单的性能评估
            if (currentTime < 1000) {
                addResult('healthResults', '🚀 性能优秀 (< 1s)', 'success');
            } else if (currentTime < 2000) {
                addResult('healthResults', '✅ 性能良好 (1-2s)', 'success');
            } else if (currentTime < 3000) {
                addResult('healthResults', '⚠️ 性能一般 (2-3s)', 'warning');
            } else {
                addResult('healthResults', '❌ 性能较差 (> 3s)', 'error');
            }

            // 更新最终结果对比
            updateComparisonResults();
        }

        function updateComparisonResults() {
            const container = document.getElementById('comparisonResults');
            container.innerHTML = '';
            
            const results = [
                {
                    target: '消除FormManager重复注册警告',
                    achieved: warnings.filter(w => w.includes('formManager') && w.includes('已存在')).length === 0,
                    metric: 'warnings.length === 0'
                },
                {
                    target: '消除VehicleConfigIntegration误报',
                    achieved: warnings.filter(w => w.includes('VehicleConfigIntegration')).length === 0,
                    metric: 'integration warnings === 0'
                },
                {
                    target: '5阶段清晰架构',
                    achieved: window.OTA?.scriptManifest?.phases?.length === 5,
                    metric: 'phases.length === 5'
                },
                {
                    target: '启动性能提升',
                    achieved: (performance.now() - startTime) < 2000,
                    metric: '< 2000ms'
                }
            ];

            results.forEach(result => {
                const statusDiv = document.createElement('div');
                statusDiv.className = `status ${result.achieved ? 'success' : 'error'}`;
                statusDiv.textContent = `${result.achieved ? '✅' : '❌'} ${result.target} (${result.metric})`;
                container.appendChild(statusDiv);
            });

            const successRate = results.filter(r => r.achieved).length / results.length * 100;
            const summaryDiv = document.createElement('div');
            summaryDiv.className = `status ${successRate >= 75 ? 'success' : successRate >= 50 ? 'warning' : 'error'}`;
            summaryDiv.textContent = `🎯 优化成功率: ${Math.round(successRate)}% (${results.filter(r => r.achieved).length}/${results.length})`;
            container.appendChild(summaryDiv);
        }

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                updateMetrics();
                addResult('startupResults', '🚀 页面加载完成，开始验证...', 'info');
                testStartupProcess();
            }, 500);
        });
    </script>
</body>
</html>